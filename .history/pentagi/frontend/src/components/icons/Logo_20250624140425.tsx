import { cn } from '@/lib/utils';

interface LogoProps extends React.SVGProps<SVGSVGElement> {
    className?: string;
}

const Logo = ({ className, ...props }: LogoProps) => {
    return (
        <svg
            viewBox="0 0 160 160"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            className={cn(className)}
            {...props}
        >
            {/* 国家电网Logo - 电力塔和电网元素 */}
            {/* 主体电力塔 */}
            <path
                d="M80 20 L70 40 L90 40 Z"
                fill="currentColor"
            />
            <rect x="78" y="40" width="4" height="80" fill="currentColor" />

            {/* 电力线路 - 横向连接线 */}
            <line x1="20" y1="50" x2="140" y2="50" stroke="currentColor" strokeWidth="2" />
            <line x1="25" y1="70" x2="135" y2="70" stroke="currentColor" strokeWidth="2" />
            <line x1="30" y1="90" x2="130" y2="90" stroke="currentColor" strokeWidth="2" />

            {/* 左侧电力塔 */}
            <path d="M40 30 L35 45 L45 45 Z" fill="currentColor" />
            <rect x="39" y="45" width="2" height="60" fill="currentColor" />

            {/* 右侧电力塔 */}
            <path d="M120 30 L115 45 L125 45 Z" fill="currentColor" />
            <rect x="119" y="45" width="2" height="60" fill="currentColor" />

            {/* 电网连接点 */}
            <circle cx="40" cy="50" r="3" fill="currentColor" />
            <circle cx="80" cy="50" r="3" fill="currentColor" />
            <circle cx="120" cy="50" r="3" fill="currentColor" />

            <circle cx="42" cy="70" r="2" fill="currentColor" />
            <circle cx="80" cy="70" r="2" fill="currentColor" />
            <circle cx="118" cy="70" r="2" fill="currentColor" />

            <circle cx="45" cy="90" r="2" fill="currentColor" />
            <circle cx="80" cy="90" r="2" fill="currentColor" />
            <circle cx="115" cy="90" r="2" fill="currentColor" />

            {/* 底部基座 */}
            <rect x="35" y="105" width="10" height="8" fill="currentColor" />
            <rect x="75" y="120" width="10" height="8" fill="currentColor" />
            <rect x="115" y="105" width="10" height="8" fill="currentColor" />

            {/* 装饰性电力符号 */}
            <path
                d="M80 130 L75 140 L85 135 L80 145 L90 140 L80 130 Z"
                fill="currentColor"
                opacity="0.7"
            />
        </svg>
    );
};

export default Logo;

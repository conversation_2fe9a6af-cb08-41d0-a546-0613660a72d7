#!/bin/bash

# 电力企业IT安全测试平台环境设置脚本
# 用于快速部署和配置整个测试环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装，请先安装Python3"
        exit 1
    fi
    
    log_success "系统依赖检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录结构..."
    
    mkdir -p logs
    mkdir -p data/postgres
    mkdir -p data/vector
    mkdir -p configs/power
    mkdir -p tools/power-security
    mkdir -p reports
    
    log_success "目录结构创建完成"
}

# 构建电力业务靶场镜像
build_lab_images() {
    log_info "构建电力业务靶场Docker镜像..."
    
    # 构建营销系统靶场
    if [ -d "labs/marketing-system-2.0" ]; then
        log_info "构建电力营销系统2.0靶场..."
        cd labs/marketing-system-2.0
        docker build -t power-labs/marketing-system:latest .
        cd ../..
        log_success "营销系统靶场镜像构建完成"
    else
        log_warning "营销系统靶场目录不存在，跳过构建"
    fi
    
    # 构建移动应用靶场
    if [ -d "labs/i-state-grid-app" ]; then
        log_info "构建i国网APP靶场..."
        cd labs/i-state-grid-app
        docker build -t power-labs/mobile-app:latest .
        cd ../..
        log_success "移动应用靶场镜像构建完成"
    else
        log_warning "移动应用靶场目录不存在，跳过构建"
    fi
    
    # 构建ERP系统靶场
    if [ -d "labs/erp-system" ]; then
        log_info "构建ERP系统靶场..."
        cd labs/erp-system
        docker build -t power-labs/erp-system:latest .
        cd ../..
        log_success "ERP系统靶场镜像构建完成"
    else
        log_warning "ERP系统靶场目录不存在，跳过构建"
    fi
}

# 创建电力专用工具镜像
create_power_tools() {
    log_info "创建电力专用安全工具..."
    
    # 创建工具目录
    mkdir -p tools/power-security/marketing-scanner
    mkdir -p tools/power-security/erp-checker
    mkdir -p tools/power-security/mobile-tester
    mkdir -p tools/power-security/compliance-validator
    
    # 创建营销系统扫描器
    cat > tools/power-security/marketing-scanner/Dockerfile << 'EOF'
FROM python:3.9-slim
RUN pip install requests beautifulsoup4 sqlparse
COPY scanner.py /app/
WORKDIR /app
ENTRYPOINT ["python", "scanner.py"]
EOF
    
    cat > tools/power-security/marketing-scanner/scanner.py << 'EOF'
#!/usr/bin/env python3
import sys
import json
import requests
import argparse

def scan_marketing_system(target, mode="comprehensive"):
    results = {
        "target": target,
        "scan_mode": mode,
        "vulnerabilities": [],
        "flags": []
    }
    
    # 模拟扫描结果
    if mode == "comprehensive":
        results["vulnerabilities"] = [
            {"type": "SQL注入", "severity": "高", "location": "/login"},
            {"type": "计费逻辑绕过", "severity": "严重", "location": "/api/billing"},
            {"type": "微服务认证绕过", "severity": "高", "location": "/api/microservices"},
            {"type": "权限提升", "severity": "中", "location": "/api/meter/data"}
        ]
        results["flags"] = [
            "FLAG{marketing_sql_injection}",
            "FLAG{billing_logic_manipulation}",
            "FLAG{microservice_auth_bypass}"
        ]
    
    return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--target", required=True)
    parser.add_argument("--mode", default="comprehensive")
    args = parser.parse_args()
    
    result = scan_marketing_system(args.target, args.mode)
    print(json.dumps(result, indent=2, ensure_ascii=False))
EOF
    
    # 构建工具镜像
    cd tools/power-security/marketing-scanner
    docker build -t power-security/marketing-scanner:latest .
    cd ../../..
    
    log_success "电力专用安全工具创建完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    # 创建.env文件
    cat > .env << 'EOF'
# 电力企业IT安全测试平台环境配置

# 基础配置
COMPOSE_PROJECT_NAME=power-security-platform
POSTGRES_DB=pentagi
POSTGRES_USER=pentagi
POSTGRES_PASSWORD=pentagi_password_2025

# 电力业务配置
POWER_MARKETING_LAB_PORT=5002
POWER_MOBILE_LAB_PORT=5003
POWER_ERP_LAB_PORT=5004

# Agent配置
POWER_AGENTS_CONFIG=/app/configs/power/power-agents.yaml
COMPLIANCE_STANDARDS_CONFIG=/app/configs/power/compliance-standards.yaml
SECURITY_POLICIES_CONFIG=/app/configs/power/security-policies.yaml

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/app/logs/power-security.log

# 安全配置
JWT_SECRET=power_security_jwt_secret_2025
API_KEY=power_security_api_key_2025
EOF
    
    log_success "环境变量配置完成"
}

# 启动服务
start_services() {
    log_info "启动电力企业IT安全测试平台..."
    
    # 启动核心服务
    docker-compose -f pentagi/docker-compose.yml up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    check_services
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    services=(
        "pentagi:8443"
        "power-marketing-lab:5002"
        "power-mobile-lab:5003"
        "power-erp-lab:5004"
        "pgvector:5432"
    )
    
    all_healthy=true
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        
        if nc -z localhost "$port" 2>/dev/null; then
            log_success "$name 服务正常 (端口 $port)"
        else
            log_error "$name 服务异常 (端口 $port)"
            all_healthy=false
        fi
    done
    
    if [ "$all_healthy" = true ]; then
        log_success "所有服务启动成功"
    else
        log_error "部分服务启动失败"
        return 1
    fi
}

# 运行测试
run_tests() {
    log_info "运行电力业务靶场测试..."
    
    if [ -f "tests/power_labs_test.py" ]; then
        python3 tests/power_labs_test.py
    else
        log_warning "靶场测试脚本不存在，跳过测试"
    fi
    
    log_info "运行电力专业化Agent测试..."
    
    if [ -f "tests/power_agents_test.py" ]; then
        python3 tests/power_agents_test.py
    else
        log_warning "Agent测试脚本不存在，跳过测试"
    fi
}

# 显示访问信息
show_access_info() {
    log_success "电力企业IT安全测试平台部署完成!"
    echo
    echo "🌐 访问地址:"
    echo "  - PentAGI主界面: https://localhost:8443"
    echo "  - 电力营销系统2.0靶场: http://localhost:5002"
    echo "  - i国网APP靶场: http://localhost:5003"
    echo "  - ERP系统靶场: http://localhost:5004"
    echo
    echo "📋 测试命令:"
    echo "  - 靶场测试: python3 tests/power_labs_test.py"
    echo "  - Agent测试: python3 tests/power_agents_test.py"
    echo
    echo "🔧 管理命令:"
    echo "  - 查看日志: docker-compose -f pentagi/docker-compose.yml logs -f"
    echo "  - 停止服务: docker-compose -f pentagi/docker-compose.yml down"
    echo "  - 重启服务: docker-compose -f pentagi/docker-compose.yml restart"
    echo
    echo "📚 配置文件:"
    echo "  - Agent配置: configs/power/power-agents.yaml"
    echo "  - 合规标准: configs/power/compliance-standards.yaml"
    echo "  - 安全策略: configs/power/security-policies.yaml"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    # 这里可以添加清理逻辑
}

# 主函数
main() {
    echo "🔋 电力企业IT安全测试平台环境设置"
    echo "========================================"
    echo
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 执行设置步骤
    check_dependencies
    create_directories
    build_lab_images
    create_power_tools
    setup_environment
    start_services
    
    # 运行测试（可选）
    if [ "${1:-}" = "--with-tests" ]; then
        run_tests
    fi
    
    # 显示访问信息
    show_access_info
    
    log_success "环境设置完成!"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
